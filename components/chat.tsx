'use client';

import type { Attachment, Message } from 'ai';
import { useChat } from 'ai/react';
import { AnimatePresence } from 'framer-motion';
import { useState, useEffect, useRef, useCallback } from 'react';
import useSWR, { useSWRConfig } from 'swr';
import { useRouter } from 'next/navigation';


import { ChatHeader } from '@/components/chat-header';
import type { Vote } from '@/lib/db/schema';
import { fetcher } from '@/lib/utils';

import { Block, type UIBlock } from './block';
import { BlockStreamHandler } from './block-stream-handler';
import { MultimodalInput } from './multimodal-input';
import { Messages } from './messages';
import type { VisibilityType } from './visibility-selector';
import {
  retryWithBackoff,
  shouldRetryError,
  createRetryState,
  updateRetryState,
  type RetryState
} from '@/lib/retry-utils';

export function Chat({
  id,
  initialMessages,
  selectedModelId,
  selectedVisibilityType,
  isReadonly,
  isUserTemporary,
  isAllMessagesView = false,
}: {
  id: string;
  initialMessages: Array<Message>;
  selectedModelId: string;
  selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
  isUserTemporary: boolean;
  isAllMessagesView?: boolean;
}) {
  const { mutate } = useSWRConfig();
  const router = useRouter();

  const [offset, setOffset] = useState(initialMessages.length);
  const [loadingMore, setLoadingMore] = useState(false);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  const {
    messages,
    setMessages,
    handleSubmit,
    input,
    setInput,
    append,
    isLoading,
    stop,
    reload,
    data: streamingData,
  } = useChat({
    id,
    body: { id, modelId: selectedModelId },
    initialMessages,
    onFinish: () => {
      mutate('/api/history');

      // Clear waiting for response state and retry state when response is finished
      setIsWaitingForResponse(false);
      setRetryState(createRetryState());
      setFailedMessage(null);

      // For temporary users, mark that they have sent messages
      if (isUserTemporary && typeof window !== 'undefined') {
        localStorage.setItem('tempUserHasMessages', 'true');
        localStorage.setItem('tempUserChatId', id);
      }
    },
    onError: (error) => {
      // Don't clear waiting state here - let the retry mechanism handle it
      console.error('Chat error:', error);
    },
  });

  // Track when we're loading older messages to prevent auto-scroll to bottom
  const isLoadingOlderMessagesRef = useRef(false);

  // Track if we're waiting for a response to the last user message
  const [isWaitingForResponse, setIsWaitingForResponse] = useState(false);

  // Retry state management
  const [retryState, setRetryState] = useState<RetryState>(createRetryState());
  const [failedMessage, setFailedMessage] = useState<{
    content: string;
    attachments: Array<Attachment>;
    options?: any;
  } | null>(null);





  const loadMoreMessages = useCallback(async () => {
    if (loadingMore) return;

    setLoadingMore(true);
    isLoadingOlderMessagesRef.current = true; // Set flag to prevent auto-scroll

    try {
      const container = messagesContainerRef.current;
      if (!container) return;

      // Store the current scroll height before loading new messages
      const prevScrollHeight = container.scrollHeight;

      let response;
      if (isAllMessagesView) {
        // For all messages view, fetch more messages from all conversations
        response = await fetch(
          `/api/chat/all-messages-paginated?limit=50&offset=${messages.length}`,
        );
      } else {
        // For individual chat view, fetch more messages from specific chat
        response = await fetch(
          `/api/chat/${id}?limit=50&offset=${messages.length}&messagesOnly=true&orderBy=desc`,
        );
      }

      if (!response.ok) {
        throw new Error('Failed to fetch more messages');
      }
      const newMessages: Message[] = await response.json();

      if (newMessages.length > 0) {
        setMessages((prevMessages) => {
          // Create a Set of existing message IDs for quick lookup
          const existingIds = new Set(prevMessages.map((msg) => msg.id));
          // Filter out any duplicate messages from newMessages
          const uniqueNewMessages = newMessages.filter(
            (msg) => !existingIds.has(msg.id),
          );
          // Add new messages at the beginning since they're older
          return [...uniqueNewMessages, ...prevMessages];
        });

        // After the new messages are rendered, adjust scroll position
        requestAnimationFrame(() => {
          if (container) {
            const newScrollHeight = container.scrollHeight;
            const addedHeight = newScrollHeight - prevScrollHeight;
            container.scrollTop = addedHeight;
          }
          // Reset the flag after scroll position is adjusted
          setTimeout(() => {
            isLoadingOlderMessagesRef.current = false;
          }, 100);
        });
      } else {
        // Reset flag even if no new messages were loaded
        isLoadingOlderMessagesRef.current = false;
      }
    } catch (error) {
      console.error('Error loading more messages:', error);
      isLoadingOlderMessagesRef.current = false; // Reset flag on error
    } finally {
      setLoadingMore(false);
    }
  }, [
    loadingMore,
    setLoadingMore,
    messagesContainerRef,
    id,
    messages,
    setMessages,
    isAllMessagesView,
  ]); // Removed unnecessary isUserTemporary dependency

  // Throttled scroll handler to improve performance
  const throttleTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastScrollTimeRef = useRef<number>(0);

  const throttledHandleScroll = useCallback(() => {
    const now = Date.now();
    const timeSinceLastCall = now - lastScrollTimeRef.current;

    if (timeSinceLastCall >= 100) {
      // Execute immediately if enough time has passed
      lastScrollTimeRef.current = now;
      const container = messagesContainerRef.current;
      if (container && container.scrollTop === 0 && !loadingMore) {
        loadMoreMessages();
      }
    } else {
      // Schedule execution for later
      if (throttleTimeoutRef.current) {
        clearTimeout(throttleTimeoutRef.current);
      }
      // Ensure timeout value is never negative
      const timeoutDelay = Math.max(1, 100 - timeSinceLastCall);
      throttleTimeoutRef.current = setTimeout(() => {
        lastScrollTimeRef.current = Date.now();
        const container = messagesContainerRef.current;
        if (container && container.scrollTop === 0 && !loadingMore) {
          loadMoreMessages();
        }
      }, timeoutDelay);
    }
  }, [loadingMore, loadMoreMessages]);

  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    container.addEventListener('scroll', throttledHandleScroll);

    return () => {
      container.removeEventListener('scroll', throttledHandleScroll);
      // Cancel any pending throttled calls
      if (throttleTimeoutRef.current) {
        clearTimeout(throttleTimeoutRef.current);
      }
    };
  }, [id, offset, loadingMore, setMessages, loadMoreMessages, isAllMessagesView, throttledHandleScroll]); // Add dependencies

  // Scroll to bottom when initial messages are loaded (but not when loading older messages)
  useEffect(() => {
    const container = messagesContainerRef.current;
    const hasMessages = messages.length > 0;
    if (container && hasMessages && !isLoadingOlderMessagesRef.current) {
      // Use requestAnimationFrame for non-blocking scroll operation
      requestAnimationFrame(() => {
        container.scrollTop = container.scrollHeight;
      });
    }
  }, [messages.length]); // Include messages.length dependency and extract complex expression

  // Optimized scroll to bottom when new messages are added (during conversation)
  const scrollToBottom = useCallback(() => {
    const container = messagesContainerRef.current;
    if (container) {
      // Use requestAnimationFrame for non-blocking scroll operation
      requestAnimationFrame(() => {
        container.scrollTop = container.scrollHeight;
      });
    }
  }, []);

  const scrollThrottleTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastScrollBottomTimeRef = useRef<number>(0);

  const throttledScrollToBottom = useCallback(() => {
    const now = Date.now();
    const timeSinceLastCall = now - lastScrollBottomTimeRef.current;

    if (timeSinceLastCall >= 50) {
      // Execute immediately if enough time has passed
      lastScrollBottomTimeRef.current = now;
      scrollToBottom();
    } else {
      // Schedule execution for later
      if (scrollThrottleTimeoutRef.current) {
        clearTimeout(scrollThrottleTimeoutRef.current);
      }
      // Ensure timeout value is never negative
      const timeoutDelay = Math.max(1, 50 - timeSinceLastCall);
      scrollThrottleTimeoutRef.current = setTimeout(() => {
        lastScrollBottomTimeRef.current = Date.now();
        scrollToBottom();
      }, timeoutDelay);
    }
  }, [scrollToBottom]);

  useEffect(() => {
    const container = messagesContainerRef.current;
    if (container && messages.length > 0 && !isLoadingOlderMessagesRef.current) {
      // Always scroll to bottom when user sends a new message
      const lastMessage = messages[messages.length - 1];
      const isUserMessage = lastMessage.role === 'user';
      const isAssistantMessage = lastMessage.role === 'assistant';

      if (isUserMessage || isLoading || isAssistantMessage) {
        // Immediately scroll to bottom for user messages, during loading, or AI responses
        scrollToBottom();
      } else {
        // For other cases, check if user is near bottom before auto-scrolling
        const isNearBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - 100;
        if (isNearBottom) {
          throttledScrollToBottom();
        }
      }
    }
  }, [messages, isLoading, scrollToBottom, throttledScrollToBottom]); // Added dependencies

  // Initialize block with default values to avoid useWindowSize dependency
  const [block, setBlock] = useState<UIBlock>({
    documentId: 'init',
    content: '',
    title: '',
    status: 'idle',
    isVisible: false,
    boundingBox: {
      top: 270, // Default to 1080/4
      left: 480, // Default to 1920/4
      width: 250,
      height: 50,
    },
  });

  // Update block position only when it becomes visible
  useEffect(() => {
    if (block.isVisible) {
      const updateBlockPosition = () => {
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;

        setBlock(prev => ({
          ...prev,
          boundingBox: {
            ...prev.boundingBox,
            top: windowHeight / 4,
            left: windowWidth / 4,
          },
        }));
      };

      updateBlockPosition();

      // Add throttled resize listener only when block is visible
      let timeoutId: NodeJS.Timeout;
      const handleResize = () => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(updateBlockPosition, 100);
      };

      window.addEventListener('resize', handleResize);
      return () => {
        window.removeEventListener('resize', handleResize);
        clearTimeout(timeoutId);
      };
    }
  }, [block.isVisible]);

  // Get message IDs for vote fetching
  const assistantMessageIds = messages
    .filter(msg => msg.role === 'assistant')
    .map(msg => msg.id)
    .filter(id => id); // Filter out any undefined IDs

  const { data: votes } = useSWR<Array<Vote>>(
    assistantMessageIds.length > 0 ? `/api/vote?messageIds=${assistantMessageIds.join(',')}` : null,
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      dedupingInterval: 60000, // Only refetch at most every 60 seconds
    },
  );

  const [attachments, setAttachments] = useState<Array<Attachment>>([]);

  // Custom handleSubmit with retry mechanism
  const handleSubmitWithRetry = useCallback(async (
    event?: { preventDefault?: () => void },
    options?: any,
    isRetryAttempt: boolean = false
  ) => {
    const messageContent = input.trim();
    if (!messageContent && !isRetryAttempt) return;

    // Store the message details for potential retry
    if (!isRetryAttempt) {
      setFailedMessage({
        content: messageContent,
        attachments: attachments || [],
        options
      });
    }

    setIsWaitingForResponse(true);

    try {
      const result = await retryWithBackoff(
        () => handleSubmit(event, options),
        {
          maxRetries: 3,
          initialDelay: 1000,
          maxDelay: 10000,
          retryCondition: shouldRetryError,
          onRetry: (error, attempt) => {
            console.log(`Retrying message send (attempt ${attempt}):`, error.message);
            setRetryState(prev => updateRetryState(prev, true, error));
          },
          timeout: 30000
        }
      );

      // Success - clear retry state and failed message
      setRetryState(createRetryState());
      setFailedMessage(null);

      return result;
    } catch (error) {
      console.error('Failed to send message after retries:', error);
      setIsWaitingForResponse(false);
      setRetryState(prev => updateRetryState(prev, false, error as Error));
      throw error;
    }
  }, [handleSubmit, input, attachments]);

  // Custom stop function that clears waiting state
  const stopWithStateClearing = useCallback(() => {
    setIsWaitingForResponse(false);
    setRetryState(createRetryState());
    return stop();
  }, [stop]);

  // Manual retry function for failed messages
  const retryFailedMessage = useCallback(async () => {
    if (!failedMessage) return;

    // Temporarily set input to the failed message content for retry
    const originalInput = input;
    setInput(failedMessage.content);

    try {
      await handleSubmitWithRetry(undefined, failedMessage.options, true);
    } finally {
      // Restore original input if retry fails
      setInput(originalInput);
    }
  }, [failedMessage, input, handleSubmitWithRetry, setInput]);

  // Handle stuck loading states - auto-retry if loading for too long
  useEffect(() => {
    if (!isWaitingForResponse || retryState.isRetrying) return;

    const stuckTimeout = setTimeout(() => {
      console.warn('Message appears to be stuck in loading state, attempting retry...');
      if (failedMessage) {
        retryFailedMessage();
      }
    }, 45000); // 45 seconds timeout for stuck messages

    return () => clearTimeout(stuckTimeout);
  }, [isWaitingForResponse, retryState.isRetrying, failedMessage, retryFailedMessage]);

  const handleClearMessages = useCallback(async () => {
    try {
      // Stop any ongoing AI generation first
      stop();

      const response = await fetch('/api/clear-messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Failed to clear messages (${response.status})`);
      }

      const responseData = await response.json();

      // Clear messages from UI immediately
      setMessages([]);

      // Clear any input that might be pending
      setInput('');

      // Reset any block state
      setBlock(prev => ({
        ...prev,
        isVisible: false,
        status: 'idle'
      }));

      // Wait a moment to ensure all operations complete
      await new Promise(resolve => setTimeout(resolve, 100));

      // For temporary users, redirect to new chat if a new chat ID was provided
      if (isUserTemporary && responseData.shouldRedirect && responseData.newChatId) {
        // Store the new chat ID for temporary users and clear the messages flag
        if (typeof window !== 'undefined') {
          localStorage.setItem('tempUserChatId', responseData.newChatId);
          localStorage.removeItem('tempUserHasMessages'); // Clear this since we're starting fresh
        }
        // Redirect to the new chat
        router.push(`/chat/${responseData.newChatId}`);
        return;
      }

      // For all messages view, refetch the filtered messages
      if (isAllMessagesView) {
        try {
          const messagesResponse = await fetch('/api/chat/all-messages-paginated?limit=25&offset=0');
          if (messagesResponse.ok) {
            const filteredMessages = await messagesResponse.json();
            setMessages(filteredMessages);
          }
        } catch (fetchError) {
          console.error('Error fetching filtered messages:', fetchError);
          // If fetching fails, just keep messages cleared
        }
      }

      // Invalidate SWR cache to ensure fresh data on next load
      mutate('/api/history');
    } catch (error) {
      console.error('Error clearing messages:', error);
      throw error;
    }
  }, [setMessages, setInput, setBlock, stop, isAllMessagesView, mutate, isUserTemporary, router]);

  return (
    <>
      <div className="flex flex-col min-w-0 h-dvh bg-background">
        <ChatHeader
          user={null}
          chatId={id}
          selectedModelId={selectedModelId}
          selectedVisibilityType={selectedVisibilityType}
          isReadonly={isReadonly}
          isUserTemporary={isUserTemporary}
          append={append}
          onClearMessagesAction={handleClearMessages}
        />

        <div className="flex-1 overflow-y-auto" ref={messagesContainerRef}>
          {loadingMore && (
            <div className="flex justify-center py-2 text-gray-500">
              Loading more messages...
            </div>
          )}
          <Messages
            chatId={id}
            block={block}
            setBlock={setBlock}
            isLoading={isLoading}
            votes={votes}
            messages={messages}
            setMessages={setMessages}
            reload={reload}
            isReadonly={isReadonly}
            isUserTemporary={isUserTemporary}
            isWaitingForResponse={isWaitingForResponse}
            retryState={retryState}
            onRetryMessage={retryFailedMessage}
          />
        </div>

        <div className="shrink-0 bg-white">
          <form className="flex mx-auto px-3 md:px-4 pt-4 pb-3 md:py-6 gap-2 w-full md:max-w-3xl">
            {!isReadonly && (
              <MultimodalInput
                chatId={id}
                input={input}
                setInput={setInput}
                handleSubmit={handleSubmitWithRetry}
                isLoading={isLoading}
                stop={stopWithStateClearing}
                attachments={attachments}
                setAttachments={setAttachments}
                messages={messages}
                setMessages={setMessages}
                append={append}
                isUserTemporary={isUserTemporary}
              />
            )}
          </form>
        </div>
      </div>

      <AnimatePresence>
        {block?.isVisible && (
          <Block
            chatId={id}
            input={input}
            setInput={setInput}
            handleSubmit={handleSubmitWithRetry}
            isLoading={isLoading}
            stop={stopWithStateClearing}
            attachments={attachments}
            setAttachments={setAttachments}
            append={append}
            block={block}
            setBlock={setBlock}
            messages={messages}
            setMessages={setMessages}
            reload={reload}
            votes={votes}
            isReadonly={isReadonly}
          />
        )}
      </AnimatePresence>

      <BlockStreamHandler streamingData={streamingData} setBlock={setBlock} />
    </>
  );
}
